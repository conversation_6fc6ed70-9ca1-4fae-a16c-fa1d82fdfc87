<input ng-if="aType!='number'" class="form-control {{aSize}}" type="{{aType}}" ng-disabled="isDisabled"  ng-model="aTextboxModel" placeholder="{{aPlaceholder}}" ng-required="aRequired" name="{{aName}}" />
<input ng-if="aType=='number'"  step="0.01" min="0" class="form-control {{aSize}}" type="{{aType}}" ng-disabled="isDisabled"  ng-model="aTextboxModel" placeholder="{{aPlaceholder}}" ng-required="aRequired" name="{{aName}}"/>

