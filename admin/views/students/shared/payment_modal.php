<?php
	include('../auth/user.php');
	$tabs = array(
		'PAYMENT'=>false,
		'VALIDATION'=>false,
		);
	switch($_MODULE){
		case 'payments':
			$tabs['PAYMENT'] = true;
		break;
		case 'validations':
			$tabs['VALIDATION'] = true;
		break;
		
	}
?>

<a-modal id="ActivePaymentModal" title="Payment Details" is-large="true" has-close="true">
	<a-modal-body>
		<form  name="PaymentConfirmationForm">
		<a-row>
			<a-col size="3">
				<m-formgroup ng-model="OPC.ActivePayment.ref_no" label="Reference No."type="'display'"></m-formgroup>
			</a-col>
			<a-col size="3">
				<m-formgroup ng-model="OPC.ActivePayment.transaction_date" label="Submitted Date" type="'display'"></m-formgroup>
			</a-col>
			<a-col size="6">
				<m-formgroup ng-model="OPC.ActivePayment.student_name" label="Student" type="'display'" size="'input-lg'"></m-formgroup>
			</a-col>
			
		</a-row>
		<a-row>
			<a-col size="6">
				<div class="form-group">
					<label for="">Attachment</label>
					<img  class="thumbnail img-responsive" alt="Proof of Payment" ng-src="{{OPC.AttachmentURL}}"/>
				</div>
			</a-col>
			<a-col size="6">
					<a-row>
						
						<a-col>
							<m-formgroup ng-model="OPC.ActivePayment.amount" name="payment_amount"  label="Amount Paid" type="'number'" ng-required="true" size="'input-lg'"></m-formgroup>
						</a-col>
						
						<a-col>
							<m-formgroup ng-model="OPC.ActivePayment.payment_reference" name="payment_reference" label="Payment Reference" ng-required="true"></m-formgroup>
						</a-col>
						<a-col size="6">
							<m-formgroup ng-model="OPC.ActivePayment.payment_method"  name="payment_method" label="Payment Method" options="OPC.PayModeOptions" ng-required="true" ></m-formgroup>
						</a-col>
						<a-col size="6">
							<m-formgroup ng-model="OPC.ActivePayment.confirmation_date" name="confirmation_date" label="Confirmation Date" type="'date'" ng-required="true"></m-formgroup>
						</a-col>
						<a-col>
							<m-formgroup ng-model="OPC.ActivePayment.notes" name="payment_notes" label="Notes" type="'textarea'" ></m-formgroup>
						</a-col>
						<a-col>
							<m-formgroup ng-model="OPC.ActivePayment.payment_status"  name="payment_status"  label="Payment Status" options="OPC.PaymentStatusOptions" ng-required="true" ></m-formgroup>
						</a-col>
					</a-row>
					<a-row>
						<a-col>
							<!-- Debug form validation state -->
							<div class="alert alert-info" style="font-size: 12px; margin-bottom: 10px;">
								<strong>Form Debug:</strong> Valid: {{PaymentConfirmationForm.$valid}} |
								Errors: {{PaymentConfirmationForm.$error | json}}
							</div>
							<a-button type="primary btn-block"  size="lg" ng-disabled="!PaymentConfirmationForm.$valid || OPC.isSaving"  ng-click="OPC.savePayment()">
								<a-glyph icon="floppy-disk"></a-glyph>
								<span ng-if="!OPC.isSaving">Save Payment</span>
								<span ng-if="OPC.isSaving">Saving...</span>
							</a-button>
						</a-col>
					</a-row>
				
			</a-col>
		</a-row>
		</form>
    </a-modal-body>
</a-modal>