"use strict";
define(['app','atomic/bomb','api'], function (app) {
    app.register.controller('OnlinePaymentsController',['$scope','api','$filter','Atomic','aModal',function ($scope,api,$filter,atomic,aModal) {
        const $selfScope = $scope;
        $scope = this;
        $scope.init = function () {
            // Initialize the controller
            $scope.payments = [];
            $scope.headers = ['Date Submitted','Ref No.','Student Name','Grade Level','Payment Type','Amount Paid','Status'];
            $scope.props = ['transaction_date','ref_no','student_name','year_level','payment_method_display','amount_display','payment_status_display'];
            $scope.isLoading = false;
             $scope.PaymentStatusOptions = [
                {id:'PENDNG',name:'Pending'},
                {id:'APPRVD',name:'Approved'},
                {id:'DCLNED',name:'Declined'}
            ];

             $scope.PayModeOptions = [
                {id:'GCASH',name:'GCash'},
                {id:'PMAYA',name:'Pay Maya'},
                {id:'BANKT',name:'Bank Transfer'},
                {id:'ONSIT',name:'On-Site Payment'},
                {id:'NOPAY',name:'Skipped Required Payment'},
                {id:'NONE',name:'No Payment'}

            ];
            $scope.loadPayments();
        };

        $scope.loadPayments = function () {
            $scope.isLoading = true;
            api.GET('online_payments', {}, $scope.success, $scope.error);
        };

        let success = function(response){
            $scope.isLoading = false;
            let payments = response.data;
            for(var i in payments){
                let payment = payments[i];
                let status = payment.payment_status;
                let transactionDate = payment.transaction_date;

                switch(status){
                    case 'APPRVD':
                        payments[i].class = 'success';
                    break;
                    case 'DCLNED':
                        payments[i].class = 'danger';
                    break;
                    case 'PENDNG':
                        payments[i].class = 'warning';
                    break;
                    case 'REVRSE':
                        payments[i].class = 'info';
                    break;
                }

                // Format transaction date
                payment.transaction_date = $filter('date')(new Date(transactionDate), 'MMM d, yyyy');

                // Format amount as currency
                payment.amount_display = $filter('currency')(payment.amount, '₱');

                // Format payment method for display
                switch(payment.payment_method){
                    case 'GCASH':
                        payment.payment_method_display = 'GCash';
                    break;
                    case 'PMAYA':
                        payment.payment_method_display = 'PayMaya';
                    break;
                    case 'BANKT':
                        payment.payment_method_display = 'Bank Transfer';
                    break;
                    case 'ONSIT':
                        payment.payment_method_display = 'On-site';
                    break;
                }

                // Format payment status for display
                switch(payment.payment_status){
                    case 'APPRVD':
                        payment.payment_status_display = 'Approved';
                    break;
                    case 'DCLNED':
                        payment.payment_status_display = 'Declined';
                    break;
                    case 'PENDNG':
                        payment.payment_status_display = 'Pending';
                    break;
                    case 'REVRSE':
                        payment.payment_status_display = 'Reversed';
                    break;
                }

                // Extract incoming level from assessment details if available
                if(payment.Assessment && payment.year_level){
                    try {

                        payment.year_level = year_level|| 'N/A';
                    } catch(e) {
                        payment.incoming_level = 'N/A';
                    }
                } else {
                    payment.incoming_level = 'N/A';
                }

                payments[i] = payment;
            }
            $scope.payments = payments;
            $scope.Meta = response.meta;
        };

        let error = function(response){
            console.error('Error loading payments:', response);
            $scope.isLoading = false;
        };

        $scope.success = success;
        $scope.error = error;

        $scope.init();

       $scope.openModal = function(item){
        aModal.open('ActivePaymentModal');
            $scope.ActivePayment = item;
            $scope.AttachmentURL = null;
            if(item.attachment)
                $scope.AttachmentURL = '../api/attachments/view/'+item.attachment.file_name;
            $scope.ActivePayment.confirmation_date = angular.copy(item.confirmation_date? new Date(item.confirmation_date): new Date());
            console.log($scope.ActivePayment);

       }
       $scope.closeModal = function() {
            $scope.ActivePayment = null;
             $scope.AttachmentURL = null;
            aModal.close('ActivePaymentModal');
       }

       $scope.savePayment = function() {
            if (!$scope.ActivePayment || !$scope.ActivePayment.id) {
                console.error('No active payment to save');
                return;
            }

            // Disable the save button to prevent multiple submissions
            $scope.isSaving = true;

            // Prepare the payment data for API
            let paymentData = {
                OnlinePayment: {
                    id: $scope.ActivePayment.id,
                    amount: $scope.ActivePayment.amount,
                    payment_reference: $scope.ActivePayment.payment_reference,
                    payment_method: $scope.ActivePayment.payment_method,
                    notes: $scope.ActivePayment.notes,
                    payment_status: $scope.ActivePayment.payment_status
                }
            };

            // Format the confirmation date if it's a Date object
            if ($scope.ActivePayment.confirmation_date instanceof Date) {
                paymentData.OnlinePayment.confirmation_date = $filter('date')($scope.ActivePayment.confirmation_date, 'yyyy-MM-dd HH:mm:ss');
            } else if ($scope.ActivePayment.confirmation_date) {
                paymentData.OnlinePayment.confirmation_date = $scope.ActivePayment.confirmation_date;
            }

            // Call the API to update the payment
            api.PUT('online_payments/edit/' + $scope.ActivePayment.id, paymentData,
                function(response) {
                    // Success callback
                    $scope.isSaving = false;
                    console.log('Payment saved successfully:', response);

                    // Show success message
                    alert('Payment updated successfully!');

                    // Close the modal
                    $scope.closeModal();

                    // Reload the payments list to reflect changes
                    $scope.loadPayments();
                },
                function(error) {
                    // Error callback
                    $scope.isSaving = false;
                    console.error('Error saving payment:', error);

                    // Show error message
                    let errorMessage = error.message || 'Failed to save payment. Please try again.';
                    alert(errorMessage);
                }
            );
       }
    }]);
});
